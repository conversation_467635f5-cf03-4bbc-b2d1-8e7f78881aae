import {CompetitorSummary} from "./CompetitorSummary";

export class VehicleAdvertDetailBase {
   FirstPricePosition: number;
   LastPriceChange: number;
   TotalChanges: number;
   TotalPriceChange: number;
   AdId: number;
   AdSiteName: string;
   RetailerSiteRetailerId: number;
   RetailerSiteId: number;
   VehicleReg: string;
   Chassis: string;
   StockNumber: string;
   RetailerIdentifier: number;
   WebSiteStockIdentifier: string;
   WebSiteSearchIdentifier: string;
   Make: string;
   Model: string;
   Drivetrain: string;
   Doors: number;
   Derivative: string;
   DerivativeId: string;
   VehicleType: string;
   Trim: string;
   EngineCapacityCC: string;
   BodyType: string;
   FuelType: string;
   TransmissionType: string;
   Colour: string;
   SpecificColour: string;
   AgeAndOwners: string;

   AttentionGrabber: string;
   Description: string;
   DaysListed: number;
   StockDate: Date | null;
   DaysInStock: number;
   DaysBookedIn:number;
   ForecourtPrice: number;
   AdvertisedPrice: number;
   AdvertisedPriceExclAdminFee:number;
   SuppliedPrice: number;
   AdminFee: number;
   StrategyPrice: number;
   TestStrategyPrice: number;
   TestStrategyDaysToSell: number;
   IncludingVat: boolean;
   PriceIndicatorRatingAtCurrentSelling: string;
   DaysToSellAtCurrentSelling: number;
   ValuationAdjPartEx: number;
   ValuationAdjTrade: number;
   ValuationAdjPrivate: number;
   ValuationAdjRetail: number;
   ValuationMktAvPartEx: number;
   ValuationMktAvTrade: number;
   ValuationMktAvPrivate: number;
   ValuationMktAvRetail: number;


   RelevantValuation: number;
   ThisVehicleValnVsAverage: number;
   VehicleHasOptionsSpecified: boolean;
   VehicleAdvertPortalOptions: string[];
   RetailRating: number;
   PerfRatingScore: number;
   PerfRating: string;
   SearchViewsYest: number;
   AdvertViewsYest: number;
   SearchViews7Day: number;
   AdvertViews7Day: number;
   RetailDemand: number;
   RetailSupply: number;
   RetailMarketCondition: number;
   ImageUrl: string;
   ImagesCount: number;
   HasVideo: boolean;
   StockItemId: number;
   OdometerReading: number | null;
   SiteOptedIntoAutoPricing: boolean;
   VatQualifying: boolean;
   CostPrice: number;
   PrepCost: number;
   PricedProfit: number;

   NationalRetailRating: number;
   NationalRetailDays: number;
   AutotraderAdvertStatus: string;
   AdvertiserAdvertStatus: string;
   LifecycleStatus: string;

   VsStrategyBanding: string; //
   NationalRetailMktCondition: string;
   LeavingSnapshotDate: Date | null;

   IsTradePricing?: boolean;
   TradeMarginPercentage?: number;
   TradeMarginAmount?: number;
   ValuationComments: string;
}

export interface VehicleAdvertDetailDTO extends VehicleAdvertDetailBase {
   LastChangeDate: string | null;
   FirstRegisteredDate: string;
   DateOnForecourt: string;

}

export class VehicleAdvertDetail implements VehicleAdvertDetailBase {
   constructor(itemIn: VehicleAdvertDetailDTO) {
      this.LastChangeDate = itemIn.LastChangeDate ? new Date(itemIn.LastChangeDate) : null;
      this.FirstRegisteredDate = new Date(itemIn.FirstRegisteredDate);
      this.DateOnForecourt = new Date(itemIn.DateOnForecourt);

      this.FirstPricePosition = itemIn.FirstPricePosition;
      this.LastPriceChange = itemIn.LastPriceChange;
      this.TotalChanges = itemIn.TotalChanges;
      this.TotalPriceChange = itemIn.TotalPriceChange;
      this.AdId = itemIn.AdId;
      this.AdSiteName = itemIn.AdSiteName;
      this.RetailerSiteRetailerId = itemIn.RetailerSiteRetailerId;
      this.RetailerSiteId = itemIn.RetailerSiteId;
      this.VehicleReg = itemIn.VehicleReg;
      this.Chassis = itemIn.Chassis;
      this.StockNumber = itemIn.StockNumber;
      this.RetailerIdentifier = itemIn.RetailerIdentifier;
      this.WebSiteStockIdentifier = itemIn.WebSiteStockIdentifier;
      this.WebSiteSearchIdentifier = itemIn.WebSiteSearchIdentifier;
      this.Make = itemIn.Make;
      this.Model = itemIn.Model;
      this.Drivetrain = itemIn.Drivetrain;
      this.Doors = itemIn.Doors;
      this.Derivative = itemIn.Derivative;
      this.DerivativeId = itemIn.DerivativeId;
      this.VehicleType = itemIn.VehicleType;
      this.Trim = itemIn.Trim;
      this.EngineCapacityCC = itemIn.EngineCapacityCC;
      this.BodyType = itemIn.BodyType;
      this.FuelType = itemIn.FuelType;
      this.TransmissionType = itemIn.TransmissionType;
      this.Colour = itemIn.Colour;
      this.SpecificColour = itemIn.SpecificColour;
      this.AgeAndOwners = itemIn.AgeAndOwners;
      this.AttentionGrabber = itemIn.AttentionGrabber;
      this.Description = itemIn.Description;
      this.DaysListed = itemIn.DaysListed;
      this.StockDate = itemIn.StockDate ? new Date(itemIn.StockDate) : null
      this.DaysInStock = itemIn.DaysInStock;
      this.DaysBookedIn = itemIn.DaysBookedIn;
      this.ForecourtPrice = itemIn.ForecourtPrice;
      this.AdvertisedPrice = itemIn.AdvertisedPrice;
      this.AdvertisedPriceExclAdminFee = itemIn.AdvertisedPriceExclAdminFee;
      this.SuppliedPrice = itemIn.SuppliedPrice;
      this.AdminFee = itemIn.AdminFee;
      this.StrategyPrice = itemIn.StrategyPrice;
      this.TestStrategyPrice = itemIn.TestStrategyPrice;
      this.TestStrategyDaysToSell = itemIn.TestStrategyDaysToSell;
      this.IncludingVat = itemIn.IncludingVat;
      this.PriceIndicatorRatingAtCurrentSelling = itemIn.PriceIndicatorRatingAtCurrentSelling;
      this.DaysToSellAtCurrentSelling = itemIn.DaysToSellAtCurrentSelling;
      this.ValuationAdjPartEx = itemIn.ValuationAdjPartEx;
      this.ValuationAdjTrade = itemIn.ValuationAdjTrade;
      this.ValuationAdjPrivate = itemIn.ValuationAdjPrivate;
      this.ValuationAdjRetail = itemIn.ValuationAdjRetail;
      this.ValuationMktAvPartEx = itemIn.ValuationMktAvPartEx;
      this.ValuationMktAvTrade = itemIn.ValuationMktAvTrade;
      this.ValuationMktAvPrivate = itemIn.ValuationMktAvPrivate;
      this.ValuationMktAvRetail = itemIn.ValuationMktAvRetail;
      this.RelevantValuation = itemIn.RelevantValuation;
      this.ThisVehicleValnVsAverage = itemIn.ThisVehicleValnVsAverage;
      this.VehicleHasOptionsSpecified = itemIn.VehicleHasOptionsSpecified;
      this.VehicleAdvertPortalOptions = itemIn.VehicleAdvertPortalOptions;
      this.RetailRating = itemIn.RetailRating;
      this.PerfRatingScore = itemIn.PerfRatingScore;
      this.PerfRating = itemIn.PerfRating;
      this.SearchViewsYest = itemIn.SearchViewsYest;
      this.AdvertViewsYest = itemIn.AdvertViewsYest;
      this.SearchViews7Day = itemIn.SearchViews7Day;
      this.AdvertViews7Day = itemIn.AdvertViews7Day;
      this.RetailDemand = itemIn.RetailDemand;
      this.RetailSupply = itemIn.RetailSupply;
      this.RetailMarketCondition = itemIn.RetailMarketCondition;
      this.ImageUrl = itemIn.ImageUrl;
      this.ImagesCount = itemIn.ImagesCount;
      this.HasVideo = itemIn.HasVideo;
      this.StockItemId = itemIn.StockItemId;
      this.OdometerReading = itemIn.OdometerReading;
      this.SiteOptedIntoAutoPricing = itemIn.SiteOptedIntoAutoPricing;
      this.VatQualifying = itemIn.VatQualifying;
      this.CostPrice = itemIn.CostPrice;
      this.PrepCost = itemIn.PrepCost;
      this.PricedProfit = itemIn.PricedProfit;

      this.NationalRetailRating = itemIn.NationalRetailRating;
      this.NationalRetailDays = itemIn.NationalRetailDays;
      this.AutotraderAdvertStatus = itemIn.AutotraderAdvertStatus;
      this.AdvertiserAdvertStatus = itemIn.AdvertiserAdvertStatus;
      this.LifecycleStatus = itemIn.LifecycleStatus;

      this.VsStrategyBanding = itemIn.VsStrategyBanding;
      this.NationalRetailMktCondition = itemIn.NationalRetailMktCondition;
      this.LeavingSnapshotDate = itemIn.LeavingSnapshotDate;
      this.ValuationComments = itemIn.ValuationComments;

      //client side props
      this.showVsStrategyLozenge = false;
      this.isTradePricing = itemIn.IsTradePricing;
      this.tradeMarginPercentage = itemIn.TradeMarginPercentage;
      this.tradeMarginAmount = itemIn.TradeMarginAmount;
   }

   //dates
   LastChangeDate: Date | null;
   FirstRegisteredDate: Date;
   DateOnForecourt: Date;

   //rest
   FirstPricePosition: number;
   LastPriceChange: number;
   TotalChanges: number;
   TotalPriceChange: number;
   AdId: number;
   AdSiteName: string;
   RetailerSiteRetailerId: number;
   RetailerSiteId: number;
   VehicleReg: string;
   Chassis: string;
   StockNumber: string;
   RetailerIdentifier: number;
   WebSiteStockIdentifier: string;
   WebSiteSearchIdentifier: string;
   Make: string;
   Model: string;
   Derivative: string;
   DerivativeId: string;
   VehicleType: string;
   Trim: string;
   EngineCapacityCC: string;
   BodyType: string;
   Drivetrain: string;
   Doors: number;
   FuelType: string;
   TransmissionType: string;
   Colour: string;
   SpecificColour: string;
   Owners: number;
   AgeAndOwners: string;


   AttentionGrabber: string;
   Description: string;
   DaysListed: number;
   StockDate: Date | null;
   DaysInStock: number;
   DaysBookedIn:number;
   ForecourtPrice: number;
   AdvertisedPrice: number;
   AdvertisedPriceExclAdminFee:number;
   SuppliedPrice: number;
   AdminFee: number;
   StrategyPrice: number;
   TestStrategyPrice: number;
   TestStrategyDaysToSell: number;
   IncludingVat: boolean;
   PriceIndicatorRatingAtCurrentSelling: string;
   DaysToSellAtCurrentSelling: number;
   ValuationAdjPartEx: number;
   ValuationAdjTrade: number;
   ValuationAdjPrivate: number;
   ValuationAdjRetail: number;
   ValuationMktAvPartEx: number;
   ValuationMktAvTrade: number;
   ValuationMktAvPrivate: number;
   ValuationMktAvRetail: number;


   RelevantValuation: number;
   ThisVehicleValnVsAverage: number;
   VehicleHasOptionsSpecified: boolean;
   VehicleAdvertPortalOptions: string[];
   RetailRating: number;
   PerfRatingScore: number;
   PerfRating: string;
   SearchViewsYest: number;
   AdvertViewsYest: number;
   SearchViews7Day: number;
   AdvertViews7Day: number;
   RetailDemand: number;
   RetailSupply: number;
   RetailMarketCondition: number;
   ImageUrl: string;
   ImagesCount: number;
   HasVideo: boolean;
   StockItemId: number;
   OdometerReading: number | null;
   SiteOptedIntoAutoPricing: boolean;
   VatQualifying: boolean;
   CostPrice: number;
   PrepCost: number;
   PricedProfit: number;

   NationalRetailRating: number;
   NationalRetailDays: number;
   AutotraderAdvertStatus: string;
   AdvertiserAdvertStatus: string;
   LifecycleStatus: string;

   VsStrategyBanding: string; //
   NationalRetailMktCondition: string;
   LeavingSnapshotDate: Date | null;
   ValuationComments: string;

   //client side props
   showVsStrategyLozenge: boolean;
   isTradePricing?: boolean;
   tradeMarginPercentage?: number;
   tradeMarginAmount?: number;
}
