using CPHI.Spark.Model;
using CPHI.Spark.Model.ViewModels;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using static System.Net.WebRequestMethods;
using System.Text.Json;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using System.Globalization;
using Microsoft.AspNetCore.DataProtection;
using System.Collections.Generic;
using CPHI.Spark.Model.ViewModels.AutoPricing.RawDataTypes;
using Microsoft.AspNetCore.Hosting;
using System.Collections.Concurrent;
using System;
using System.Formats.Tar;
using log4net;
using CPHI.Spark.Model.ViewModels.AutoPricing;
using CPHI.Spark.Model.AutoPrice;
using CPHI.Spark.Model.Services;
using Newtonsoft.Json.Linq;
//using MoreLinq;

namespace CPHI.Spark.DataAccess.AutoPrice
{

   public interface IRallyeApiTokenClient
   {
      Task<TokenResponse> CheckExpiryAndRegenerate(TokenResponse _bearerToken);
      Task<TokenResponse> GetToken();

   }



   public class RallyeApiTokenClient : IRallyeApiTokenClient
   {

      private readonly HttpClient _httpClient;
      private string rallyeApiKey;
      private string rallyeApiSecret;
      private string rallyeBaseURL;



      public RallyeApiTokenClient(IHttpClientFactory httpClientFactory, string rallyeApiKeyIn, string rallyeApiSecretIn, string rallyeBaseURLIn)
      {
         _httpClient = httpClientFactory.CreateClient();

         rallyeApiKey = rallyeApiKeyIn;
         rallyeApiSecret = rallyeApiSecretIn;
         rallyeBaseURL = rallyeBaseURLIn;
      }


      public async Task<TokenResponse> GetToken()
      {

         var url = $"{rallyeBaseURL}/authenticate";
         var request = new HttpRequestMessage(HttpMethod.Post, url);

         request.Content = new FormUrlEncodedContent(new Dictionary<string, string>
                {
                    { "key", rallyeApiKey },
                    { "secret", rallyeApiSecret }
                });


         var response = await _httpClient.SendAsync(request);

         if (response.IsSuccessStatusCode)
         {
            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(responseContent);
            return tokenResponse;
         }
         else
         {
            throw new Exception($"Unable to retrieve token: {response.StatusCode}");
         }
      }



      public async Task<TokenResponse> CheckExpiryAndRegenerate(TokenResponse _bearerToken)
      {
         if (_bearerToken == null)
         {
            return await GetToken();
         }

            DateTime expiryTime = DateTime.Parse(_bearerToken.Expires);

         if (expiryTime.AddMinutes(-1) > DateTime.Now)
         {
            //Console.WriteLine($"Expiry time is {expiryTime.ToString("hh:mm")}, now is {DateTime.Now.ToString("hh:mm")}. Token has not expired  ");
            return _bearerToken;
         }

         //re-generate token on expiry.
         var newToken = await GetToken();
         return newToken;
      }

   }
}
