export class VehicleAdvertWithRating {
   constructor(itemIn: VehicleAdvertWithRatingDTO) {
      this.AdId = itemIn.AdId;
      this.RetailerSiteName = itemIn.RetailerSiteName;
      this.RetailerSiteId = itemIn.RetailerSiteId;
      this.RegionName = itemIn.RegionName;
      this.VehicleReg = itemIn.VehicleReg;
      this.Chassis = itemIn.Chassis;
      this.StockNumber = itemIn.StockNumber;
      this.RetailerIdentifier = itemIn.RetailerIdentifier;
      this.WebSiteStockIdentifier = itemIn.WebSiteStockIdentifier;
      this.WebSiteSearchIdentifier = itemIn.WebSiteSearchIdentifier;
      this.Make = itemIn.Make;
      this.Model = itemIn.Model;
      this.ModelCleanedUp = itemIn.ModelCleanedUp;
      this.Derivative = itemIn.Derivative;
      this.VehicleType = itemIn.VehicleType;
      this.LifecycleStatus = itemIn.LifecycleStatus;
      this.Trim = itemIn.Trim;
      this.BodyType = itemIn.BodyType;
      this.FuelType = itemIn.FuelType;
      this.OdometerReading = itemIn.OdometerReading;
      this.TransmissionType = itemIn.TransmissionType;
      this.FirstRegisteredDate = itemIn.FirstRegisteredDate;
      this.Colour = itemIn.Colour;
      this.SpecificColour = itemIn.SpecificColour;
      this.Owners = itemIn.Owners;
      this.AgeAndOwners = itemIn.AgeAndOwners;
      this.DateOnForecourt = itemIn.DateOnForecourt;
      this.AttentionGrabber = itemIn.AttentionGrabber;
      this.ActualEndDate = itemIn.ActualEndDate;
      this.OptOutBy = itemIn.OptOutBy;
      this.DaysListed = itemIn.DaysListed;
      this.DaysInStock = itemIn.DaysInStock;
      this.DaysBookedIn = itemIn.DaysBookedIn;
      this.AgeDaysListedFrom = itemIn.AgeDaysListedFrom;
      this.ForecourtPrice = itemIn.ForecourtPrice;
      this.AdminFee = itemIn.AdminFee;
      this.AdvertisedPrice = itemIn.AdvertisedPrice;
      this.AdvertisedPriceExclAdminFee = itemIn.AdvertisedPriceExclAdminFee;
      this.SIV = itemIn.SIV;
      this.OriginalPurchasePrice = itemIn.OriginalPurchasePrice;
      this.IncludingVat = itemIn.IncludingVat;
      this.PriceIndicatorRating = itemIn.PriceIndicatorRating;
      this.DaysToSellAtCurrentSelling = itemIn.DaysToSellAtCurrentSelling;
      this.NationalRetailDaysToSell = itemIn.NationalRetailDaysToSell;
      this.ValuationMktAvRetail = itemIn.ValuationMktAvRetail;
      this.ValuationMktAvPartEx = itemIn.ValuationMktAvPartEx;
      this.ValuationMktAvPrivate = itemIn.ValuationMktAvPrivate;
      this.ValuationMktAvRetailExVat = itemIn.ValuationMktAvRetailExVat;
      this.ValuationMktAvTrade = itemIn.ValuationMktAvTrade;
      this.ValuationAdjRetail = itemIn.ValuationAdjRetail;
      this.ValuationAdjPartEx = itemIn.ValuationAdjPartEx;
      this.ValuationAdjPrivate = itemIn.ValuationAdjPrivate;
      this.ValuationAdjRetailExVat = itemIn.ValuationAdjRetailExVat;
      this.ValuationAdjTrade = itemIn.ValuationAdjTrade;
      this.RelevantValuation = itemIn.RelevantValuation;
      this.ThisVehicleValnVsAverage = itemIn.ThisVehicleValnVsAverage;
      this.RetailRating = itemIn.RetailRating;
      this.NationalRetailRating = itemIn.NationalRetailRating;
      this.RetailDemand = itemIn.RetailDemand;
      this.RetailSupply = itemIn.RetailSupply;
      this.RetailMarketCondition = itemIn.RetailMarketCondition;
      this.NationalRetailMarketCondition = itemIn.NationalRetailMarketCondition;
      this.PerfRatingScore = itemIn.PerfRatingScore;
      this.PerfRating = itemIn.PerfRating;
      this.SearchViewsYest = itemIn.SearchViewsYest;
      this.AdvertViewsYest = itemIn.AdvertViewsYest;
      this.SearchViews7Day = itemIn.SearchViews7Day;
      this.AdvertViews7Day = itemIn.AdvertViews7Day;
      this.StockItemId = itemIn.StockItemId;
      this.ImageURL = itemIn.ImageURL;
      this.IsMissingImages = itemIn.IsMissingImages;
      this.HasImages = itemIn.HasImages;
      this.NoVideo = itemIn.NoVideo;

      this.PriceLow = itemIn.PriceLow;
      this.PriceGreat = itemIn.PriceGreat;
      this.PriceGood = itemIn.PriceGood;
      this.PriceFair = itemIn.PriceFair;
      this.PriceHigh = itemIn.PriceHigh;

      this.PriceChangeLow = itemIn.PriceChangeLow;
      this.PriceChangeGreat = itemIn.PriceChangeGreat;
      this.PriceChangeGood = itemIn.PriceChangeGood;
      this.PriceChangeFair = itemIn.PriceChangeFair;
      this.PriceChangeHigh = itemIn.PriceChangeHigh;
      
      this.NoAttentionGrabber = itemIn.NoAttentionGrabber;
      this.IsLowQuality = itemIn.IsLowQuality;
      this.StrategyPrice = itemIn.StrategyPrice;
      this.VsStrategyPrice = itemIn.VsStrategyPrice;
      this.StrategyPriceHasBeenCalculated = itemIn.StrategyPriceHasBeenCalculated;
      this.TestStrategyPrice = itemIn.TestStrategyPrice;
      this.StrategyPricePP = itemIn.StrategyPricePP;
      this.TestStrategyPricePP = itemIn.TestStrategyPricePP;
      this.VsTestStrategyPrice = itemIn.VsTestStrategyPrice;
      this.TestStrategyDaysToSell = itemIn.TestStrategyDaysToSell;
      this.LastCommentName = itemIn.LastCommentName;
      this.LastCommentText = itemIn.LastCommentText;
      this.CurrentStrategyName = itemIn.CurrentStrategyName;
      this.VsStrategyBanding = itemIn.VsStrategyBanding;
      this.VsTestStrategyBanding = itemIn.VsTestStrategyBanding;
      this.DailySearchViewsLast7 = itemIn.DailySearchViewsLast7;
      this.DailyAdvertViewsLast7 = itemIn.DailyAdvertViewsLast7;
      this.AgeBand = itemIn.AgeBand;
      this.RetailRatingBand = itemIn.RetailRatingBand;
      this.PerformanceRatingScoreBand = itemIn.PerformanceRatingScoreBand;
      this.DaysListedBand = itemIn.DaysListedBand;
      this.DaysInStockBand = itemIn.DaysInStockBand;
      this.DaysBookedInBand = itemIn.DaysBookedInBand;
      this.ValueBand = itemIn.ValueBand;
      this.OnBrand = itemIn.OnBrand;
      this.SiteBrand = itemIn.SiteBrand;
      this.SimpleBrand = itemIn.SimpleBrand;
      this.PricePosition = itemIn.PricePosition;
      this.CostPrice = itemIn.CostPrice;
      this.PrepCost = itemIn.PrepCost;
      this.StockSource = itemIn.StockSource;
      this.IsOptedOut = itemIn.IsOptedOut;
      this.OptedOutBy = itemIn.OptedOutBy;
      this.WhenOptedOut = itemIn.WhenOptedOut;
      this.OptedOutUntil = itemIn.OptedOutUntil;
      this.AutoOptOutType = itemIn.AutoOptOutType;
      this.LastPriceChangeValue = itemIn.LastPriceChangeValue;
      this.DaysSinceLastPriceChange = itemIn.DaysSinceLastPriceChange;
      this.WhenPriceLastManuallyChanged = itemIn.WhenPriceLastManuallyChanged;
      this.WhoLastManuallyChanged = itemIn.WhoLastManuallyChanged;
      this.TotalManualPriceChangesCount = itemIn.TotalManualPriceChangesCount;
      this.TotalManualPriceChangesValue = itemIn.TotalManualPriceChangesValue;
      this.IsVatQ = itemIn.IsVatQ;
      this.PricedProfit = itemIn.PricedProfit;
      this.PortalOptions = itemIn.PortalOptions;
      this.AutotraderAdvertStatus = itemIn.AutotraderAdvertStatus;
      this.AdvertiserAdvertStatus = itemIn.AdvertiserAdvertStatus;
      this.DaysToSellAtNewPrice = itemIn.DaysToSellAtNewPrice;
      this.PriceIndicatorAtNewPrice = itemIn.PriceIndicatorAtNewPrice;
      this.NewPrice = itemIn.NewPrice;
      this.NewPriceExAdminFee = itemIn.NewPriceExAdminFee;
      this.IsSmallPriceChange = itemIn.IsSmallPriceChange;
      this.IsKeyChange = itemIn.IsKeyChange;
      this.NewPP = itemIn.NewPP;
      this.FirstPrice = itemIn.FirstPrice;
      this.TotalPriceChanges = itemIn.TotalPriceChanges;
      this.DailyPriceMovesCount = itemIn.DailyPriceMovesCount;
      this.MostRecentDailyPriceMove = itemIn.MostRecentDailyPriceMove;
      this.MostRecentDailyPriceMoveDate = itemIn.MostRecentDailyPriceMoveDate;
      this.ModelSellRate = itemIn.ModelSellRate;
      this.CompetitorCount = itemIn.CompetitorCount;
      this.AveragePP = itemIn.AveragePP;

      this.PPAverageFranchised = itemIn.PPAverageFranchised;
      this.PPAverageIndependents = itemIn.PPAverageIndependents;
      this.PPAveragePrivates = itemIn.PPAveragePrivates;
      this.PPAverageSupermarkets = itemIn.PPAverageSupermarkets;
      this.HighestPP = itemIn.HighestPP;
      this.LowestPP = itemIn.LowestPP;
      this.OurPPRank = itemIn.OurPPRank;
      this.OurValueRank = itemIn.OurValueRank;
      this.CheapestSellerName = itemIn.CheapestSellerName;
      this.CheapestSellerType = itemIn.CheapestSellerType;
      this.CheapestVehicle = itemIn.CheapestVehicle;
      this.OnlyVehicle = itemIn.OnlyVehicle;
      this.AllPrices = itemIn.AllPrices;
      this.OurPPRankPct = itemIn.OurPPRankPct;
      this.PriceUpMaintainRank = itemIn.PriceUpMaintainRank;
      this.PriceDownImproveRank = itemIn.PriceDownImproveRank;
      this.PriceToBeCheapest = itemIn.PriceToBeCheapest;
      this.DMSSellingPrice = itemIn.DMSSellingPrice;
      this.VsDMSSellingPrice = itemIn.VsDMSSellingPrice;
      this.ValuationMonthPlus1 = itemIn.ValuationMonthPlus1;
      this.ValuationMonthPlus2 = itemIn.ValuationMonthPlus2;
      this.ValuationMonthPlus3 = itemIn.ValuationMonthPlus3;

      this.ValuationChangeMonthPlus1 = itemIn.ValuationChangeMonthPlus1;
      this.ValuationChangeMonthPlus2 = itemIn.ValuationChangeMonthPlus2;
      this.ValuationChangeMonthPlus3 = itemIn.ValuationChangeMonthPlus3;

      this.PhysicalLocation = itemIn.PhysicalLocation;
      this.VehicleTypeDesc = itemIn.VehicleTypeDesc;
      this.MarketPositionScore = itemIn.MarketPositionScore;
      this.PriceIndicatorRatingAtCurrentSelling = itemIn.PriceIndicatorRatingAtCurrentSelling;
      this.AllImageURLs = itemIn.AllImageURLs;
      this.ImagesCount = itemIn.ImagesCount;
      this.ImagesBand = itemIn.ImagesBand;
      this.EngineCapacityCC = itemIn.EngineCapacityCC;

      this.ThisVehicleValnVsAverage = itemIn.ValuationAdjRetail - itemIn.ValuationMktAvRetail;
      this.todayPriceChange = itemIn.NewPrice === 0 ? 0 : itemIn.NewPrice - itemIn.AdvertisedPrice;
      this.siteModelClean = `${itemIn.RetailerSiteName}|${itemIn.ModelCleanedUp}`;
      this.count = 1;
      this.testStrategyPriceVsStrategyPrice =
         itemIn.TestStrategyPrice === 0 || itemIn.StrategyPrice === 0
            ? 0
            : itemIn.TestStrategyPrice - itemIn.StrategyPrice;
      this.DaysSinceMostRecentPriceMove = itemIn.DaysSinceMostRecentPriceMove;

      this.StockDate = itemIn.StockDate;
      this.DateBookedIn = itemIn.DateBookedIn;
      this.DaysToAdvertise = itemIn.DaysToAdvertise;

      this.BcaVin = itemIn.BcaVin;
      this.BcaMileage = itemIn.BcaMileage;
      this.DaysOnAllSites = itemIn.DaysOnAllSites;
      this.NumberOfPreviousSales = itemIn.NumberOfPreviousSales;
      this.V5Status = itemIn.V5Status;
      this.ServiceHistory = itemIn.ServiceHistory;
      this.Runner = itemIn.Runner;
      this.SalesComment = itemIn.SalesComment;
      this.HoldDate = itemIn.HoldDate;
      this.HoldCode = itemIn.HoldCode;
      this.HoldDescription = itemIn.HoldDescription;

      // metrics

      // retail rating
      if (itemIn.RetailRating < 20) {
         this.rrU20Count = 1;
      } else if (itemIn.RetailRating < 40) {
         this.rrU40Count = 1;
      } else if (itemIn.RetailRating < 60) {
         this.rrU60Count = 1;
      } else if (itemIn.RetailRating < 80) {
         this.rrU80Count = 1;
      } else if (itemIn.RetailRating > 0) {
         this.rrO80Count = 1;
      }

      // days listed
      if (itemIn.DaysListed < 20) {
         this.dlU20Count = 1;
      } else if (itemIn.DaysListed < 40) {
         this.dlU40Count = 1;
      } else if (itemIn.DaysListed < 60) {
         this.dlU60Count = 1;
      } else {
         this.dlO60Count = 1;
      }

      // vs strategy
      switch (itemIn.VsStrategyBanding) {
         case "UnderPriced":
            this.underStrategyCount = 1;
            break;
         case "VeryUnderPriced":
            this.veryUnderStrategyCount = 1;
            break;
         case "OverPriced":
            this.overStrategyCount = 1;
            break;
         case "VeryOverPriced":
            this.veryOverStrategyCount = 1;
            break;
         case "OnStrategyPrice":
            this.onStrategyCount = 1;
            break;
         default:
            this.noStrategyCount = 1;
      }
   }

   AdId: number;
   RetailerSiteName: string;
   RetailerSiteId: number;
   RegionName: string;
   VehicleReg: string;
   Chassis: string;
   StockNumber: string;
   RetailerIdentifier: number;
   WebSiteStockIdentifier: string;
   WebSiteSearchIdentifier: string;
   Make: string;
   Model: string;
   ModelCleanedUp: string;
   Derivative: string;
   VehicleType: string;
   LifecycleStatus: string;
   Trim: string;
   BodyType: string;
   FuelType: string;
   OdometerReading: number;
   TransmissionType: string;
   FirstRegisteredDate: Date | string | null;
   Colour: string;
   SpecificColour: string;
   AgeAndOwners: string;
   Owners: number;
   DateOnForecourt: Date | string | null;
   AttentionGrabber: string;
   ActualEndDate: Date | string | null;
   OptOutBy: string;
   DaysListed: number | null;
   DaysInStock: number | null;
   DaysBookedIn: number | null;
   AgeDaysListedFrom: string;
   ForecourtPrice: number;
   AdminFee: number;
   AdvertisedPrice: number;
   AdvertisedPriceExclAdminFee: number;
   SIV: number;
   OriginalPurchasePrice: number;
   IncludingVat: boolean;
   PriceIndicatorRating: string;
   DaysToSellAtCurrentSelling: number | null;
   NationalRetailDaysToSell: number | null;
   ValuationMktAvRetail: number | null;
   ValuationMktAvPartEx: number | null;
   ValuationMktAvPrivate: number | null;
   ValuationMktAvRetailExVat: number | null;
   ValuationMktAvTrade: number | null;
   ValuationAdjRetail: number | null;
   ValuationAdjPartEx: number | null;
   ValuationAdjPrivate: number | null;
   ValuationAdjRetailExVat: number | null;
   ValuationAdjTrade: number | null;
   RelevantValuation: number | null;
   ThisVehicleValnVsAverage: number;
   RetailRating: number | null;
   NationalRetailRating: number | null;
   RetailDemand: number;
   RetailSupply: number;
   RetailMarketCondition: number;
   NationalRetailMarketCondition: number;
   PerfRatingScore: number | null;
   PerfRating: string;
   SearchViewsYest: number | null;
   AdvertViewsYest: number | null;
   SearchViews7Day: number | null;
   AdvertViews7Day: number | null;
   StockItemId: number;
   ImageURL: string;
   IsMissingImages: boolean;
   HasImages: boolean;
   NoVideo: boolean;

      PriceLow:number;
   PriceGreat:number;
   PriceGood:number;
   PriceFair:number;
   PriceHigh:number;
   PriceChangeLow:number;
   PriceChangeGreat:number;
   PriceChangeGood:number;
   PriceChangeFair:number;
   PriceChangeHigh:number;


   NoAttentionGrabber: boolean;
   IsLowQuality: boolean;
   StrategyPrice: number;
   TestStrategyPrice: number;
   StrategyPricePP:number;
   TestStrategyPricePP:number
   TestStrategyDaysToSell: number;
   StrategyPriceHasBeenCalculated: boolean;
   VsStrategyPrice: number;
   VsTestStrategyPrice: number;
   LastCommentName: string;
   LastCommentText: string;
   CurrentStrategyName: string;
   VsStrategyBanding: string;
   VsTestStrategyBanding: string;
   DailySearchViewsLast7: number[];
   DailyAdvertViewsLast7: number[];
   AgeBand: string;
   RetailRatingBand: string;
   PerformanceRatingScoreBand: string;
   DaysListedBand: string;
   DaysInStockBand: string;
   DaysBookedInBand: string;
   ValueBand: string;
   OnBrand: boolean;
   SiteBrand: string;
   SimpleBrand: string;
   PricePosition: number;
   CostPrice: number;
   PrepCost: number;
   StockSource: string;
   IsOptedOut: boolean;
   OptedOutBy: string;
   WhenOptedOut: Date;
   OptedOutUntil: Date;
   AutoOptOutType: string;
   LastPriceChangeValue: number;
   DaysSinceLastPriceChange: number;
   WhenPriceLastManuallyChanged: Date;
   WhoLastManuallyChanged: string;
   TotalManualPriceChangesCount: number;
   TotalManualPriceChangesValue: number;
   IsVatQ: boolean;
   PricedProfit: number;
   PortalOptions: string;
   AutotraderAdvertStatus: string;
   AdvertiserAdvertStatus: string;
   DaysToSellAtNewPrice: number;
   PriceIndicatorAtNewPrice: string;
   NewPrice: number;
   NewPriceExAdminFee: number;
   IsSmallPriceChange: boolean;
   IsKeyChange: boolean;
   NewPP: number;
   FirstPrice: number;
   TotalPriceChanges: number;
   DailyPriceMovesCount: number;
   MostRecentDailyPriceMove: number;
   MostRecentDailyPriceMoveDate: Date | string | null;
   ModelSellRate: number;
   CompetitorCount: number;
   AveragePP: number | null;
   PPAverageFranchised: number;
   PPAverageIndependents: number;
   PPAveragePrivates: number;
   PPAverageSupermarkets: number;
   HighestPP: number | null;
   LowestPP: number | null;
   OurPPRank: number;
   OurValueRank: number;
   CheapestSellerName: string;
   CheapestSellerType: string;
   CheapestVehicle: boolean;
   OnlyVehicle: boolean;
   AllPrices: string;
   OurPPRankPct: number;
   PriceUpMaintainRank: number;
   PriceDownImproveRank: number;
   PriceToBeCheapest: number;
   DMSSellingPrice: number;
   VsDMSSellingPrice: number;
   ValuationMonthPlus1: number;
   ValuationMonthPlus2: number;
   ValuationMonthPlus3: number;
   ValuationChangeMonthPlus1:number;
   ValuationChangeMonthPlus2:number;
   ValuationChangeMonthPlus3:number;

   PhysicalLocation: string;
   VehicleTypeDesc: string;

   MarketPositionScore: number;
   PriceIndicatorRatingAtCurrentSelling: string;
   AllImageURLs: string;
   ImagesCount: number;
   ImagesBand: string;
   EngineCapacityCC: number;

   // local props
   todayPriceChange: number;
   siteModelClean: string;
   testStrategyPriceVsStrategyPrice: number;
   count: number;
   rrU20Count: number;
   rrU40Count: number;
   rrU60Count: number;
   rrU80Count: number;
   rrO80Count: number;

   dlU20Count: number;
   dlU40Count: number;
   dlU60Count: number;
   dlO60Count: number;

   noStrategyCount: number;
   underStrategyCount: number;
   veryUnderStrategyCount: number;
   overStrategyCount: number;
   veryOverStrategyCount: number;
   onStrategyCount: number;
   DaysSinceMostRecentPriceMove: number;
   StockDate: Date | string | null;
   DateBookedIn: Date | string | null;
   DaysToAdvertise: Date | string | null;

   // From BcaStock
   BcaVin: string;
   BcaMileage: number;
   DaysOnAllSites: number;
   NumberOfPreviousSales: number;
   V5Status: boolean;
   ServiceHistory: boolean;
   Runner: string;
   SalesComment: string;
   HoldDate: Date | string | null;
   HoldCode: string;
   HoldDescription: string;
}

export class VehicleAdvertWithRatingDTO {
   AdId: number;
   RetailerSiteName: string;
   RetailerSiteId: number;
   RegionName: string;
   VehicleReg: string;
   Chassis: string;
   StockNumber: string;
   RetailerIdentifier: number;
   WebSiteStockIdentifier: string;
   WebSiteSearchIdentifier: string;
   Make: string;
   Model: string;
   ModelCleanedUp: string;
   Derivative: string;
   VehicleType: string;
   LifecycleStatus: string;
   Trim: string;
   BodyType: string;
   FuelType: string;
   OdometerReading: number;
   TransmissionType: string;
   FirstRegisteredDate: Date | string | null;
   Colour: string;
   SpecificColour: string;
   AgeAndOwners: string;
   Owners: number;
   DateOnForecourt: Date | string | null;
   AttentionGrabber: string;
   ActualEndDate: Date | string | null;
   OptOutBy: string;
   DaysListed: number | null;
   DaysInStock: number | null;
   DaysBookedIn: number | null;
   AgeDaysListedFrom: string;
   ForecourtPrice: number;
   AdminFee: number;
   AdvertisedPrice: number;
   AdvertisedPriceExclAdminFee:number;
   SIV: number;
   OriginalPurchasePrice: number;
   IncludingVat: boolean;
   PriceIndicatorRating: string;
   DaysToSellAtCurrentSelling: number | null;
   NationalRetailDaysToSell: number | null;
   ValuationMktAvRetail: number | null;
   ValuationMktAvPartEx: number | null;
   ValuationMktAvPrivate: number | null;
   ValuationMktAvRetailExVat: number | null;
   ValuationMktAvTrade: number | null;

   ValuationAdjRetail: number | null;
   ValuationAdjPartEx: number | null;
   ValuationAdjPrivate: number | null;
   ValuationAdjRetailExVat: number | null;
   ValuationAdjTrade: number | null;

   RelevantValuation: number | null;

   ThisVehicleValnVsAverage: number;
   RetailRating: number | null;
   NationalRetailRating: number | null;
   RetailDemand: number;
   RetailSupply: number;
   RetailMarketCondition: number;
   NationalRetailMarketCondition: number;
   // PriceFactor: number | null;
   // MatrixPrice: number | null;
   PerfRatingScore: number | null;
   PerfRating: string;
   SearchViewsYest: number | null;
   AdvertViewsYest: number | null;
   SearchViews7Day: number | null;
   AdvertViews7Day: number | null;
   StockItemId: number;

   // new props
   ImageURL: string;
   IsMissingImages: boolean;
   HasImages: boolean;
   NoVideo: boolean;

   PriceLow:number;
   PriceGreat:number;
   PriceGood:number;
   PriceFair:number;
   PriceHigh:number;
   PriceChangeLow:number;
   PriceChangeGreat:number;
   PriceChangeGood:number;
   PriceChangeFair:number;
   PriceChangeHigh:number;



   NoAttentionGrabber: boolean;
   IsLowQuality: boolean;
   StrategyPrice: number;
   TestStrategyPrice: number;
   StrategyPricePP:number;
   TestStrategyPricePP:number
   TestStrategyDaysToSell: number;
   StrategyPriceHasBeenCalculated: boolean;
   VsStrategyPrice: number;
   VsTestStrategyPrice: number;
   LastCommentName: string;
   LastCommentText: string;
   CurrentStrategyName: string;
   VsStrategyBanding: string;
   VsTestStrategyBanding: string;

   DailySearchViewsLast7: number[];
   DailyAdvertViewsLast7: number[];

   AgeBand: string;
   RetailRatingBand: string;
   PerformanceRatingScoreBand: string;
   DaysListedBand: string;
   DaysInStockBand: string;
   DaysBookedInBand: string;
   ValueBand: string;
   OnBrand: boolean;
   SiteBrand: string;
   SimpleBrand: string;
   PricePosition: number;

   // PendingAutoChangeWasPrice: number;
   // PendingAutoChangeNowPrice: number;
   // PendingAutoChangePriceFactor: number;

   // SPK-3717
   CostPrice: number;
   PrepCost: number;
   StockSource: string;
   IsOptedOut: boolean;
   OptedOutBy: string;
   WhenOptedOut: Date;
   OptedOutUntil: Date;
   AutoOptOutType: string;
   LastPriceChangeValue: number;
   DaysSinceLastPriceChange: number;
   WhenPriceLastManuallyChanged: Date;
   WhoLastManuallyChanged: string;
   TotalManualPriceChangesCount: number;
   TotalManualPriceChangesValue: number;
   IsVatQ: boolean;
   PricedProfit: number;
   PortalOptions: string;
   AutotraderAdvertStatus: string;
   AdvertiserAdvertStatus: string;

   DaysToSellAtNewPrice: number;
   PriceIndicatorAtNewPrice: string;
   NewPrice: number;
   NewPriceExAdminFee: number;
   IsSmallPriceChange: boolean;
   IsKeyChange: boolean;
   NewPP: number;

   FirstPrice: number;
   TotalPriceChanges: number;
   DailyPriceMovesCount: number;
   MostRecentDailyPriceMove: number;
   MostRecentDailyPriceMoveDate: Date | string | null;

   ModelSellRate: number;

   // SPK-4244 Competitor Info
   CompetitorCount: number;
   AveragePP: number | null;
   // StDevPP: number | null; hold for now
   PPAverageFranchised: number;
   PPAverageIndependents: number;
   PPAveragePrivates: number;
   PPAverageSupermarkets: number;

   HighestPP: number | null;
   LowestPP: number | null;
   OurPPRank: number;
   OurValueRank: number;
   CheapestSellerName: string;
   CheapestSellerType: string;
   CheapestVehicle: boolean;
   OnlyVehicle: boolean;
   AllPrices: string;
   OurPPRankPct: number;

   PriceUpMaintainRank: number;
   PriceDownImproveRank: number;
   PriceToBeCheapest: number;
   DMSSellingPrice: number;
   VsDMSSellingPrice: number;

   ValuationMonthPlus1: number;
   ValuationMonthPlus2: number;
   ValuationMonthPlus3: number;
   ValuationChangeMonthPlus1: number;
   ValuationChangeMonthPlus2: number;
   ValuationChangeMonthPlus3: number;

   PhysicalLocation: string;
   VehicleTypeDesc: string;

   MarketPositionScore: number;
   PriceIndicatorRatingAtCurrentSelling: string;
   AllImageURLs: string;
   ImagesCount: number;
   ImagesBand: string;
   EngineCapacityCC: number;

   DaysSinceMostRecentPriceMove: number;

   StockDate: Date | string | null;
   DateBookedIn: Date | string | null;
   DaysToAdvertise: Date | string | null;

   // From BcaBranchStock
   BcaVin: string;
   BcaMileage: number;
   DaysOnAllSites: number;
   NumberOfPreviousSales: number;
   V5Status: boolean;
   ServiceHistory: boolean;
   Runner: string;
   SalesComment: string;
   HoldDate: Date | string | null;
   HoldCode: string;
   HoldDescription: string;
}
