﻿using CPHI.Spark.Model;
using CPHI.Spark.Model.Services;
using CPHI.Spark.Reporter.Services;
using CPHI.Spark.Reporter.Services.AutoPrice;
using CPHI.Spark.Repository;
using log4net;
using Quartz;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Net.Http;
using CPHI.Spark.DataAccess.AutoPrice;

namespace CPHI.Spark.Reporter.Jobs.AutoPrice
{

   public class FetchAndGenerateAutoTraderPrices_Job : IJob
   {
      private static readonly ILog logger = LogManager.GetLogger(typeof(FetchAndGenerateAutoTraderPrices_Job));
      private RallyeApiTokenClient rallyeTokenClient;

      //private DateTime monthToRunFor;

      // ------------------------
      // START HERE 
      // ------------------------
      public async Task Execute(IJobExecutionContext context)
      {
         logger.Info("");
         logger.Info("=========================================================================================");
         logger.Info("Starting FetchAndGenerateAutoTraderPrices_Job");
         HttpClient httpClient =  HttpClientFactoryService.HttpClientFactory.CreateClient();

         rallyeTokenClient = new RallyeApiTokenClient(HttpClientFactoryService.HttpClientFactory, ConfigService.RallyeApiKey, ConfigService.RallyeApiSecret, ConfigService.RallyeBaseURL);
         var tokenResponse = (await rallyeTokenClient.GetToken());

         int end = 0;

         try
         {
            List<DealerGroupName> fetchGroups = ConfigService.BuildEnumList(ConfigService.FetchAdvertsGroups);

            Stopwatch sw = new Stopwatch();
            sw.Start();

            //----------------------------------------------------------------
            // FetchAdverts
            //----------------------------------------------------------------
            try
            {
               FetchAutoTraderService fetchAutoTraderService = new FetchAutoTraderService(HttpClientFactoryService.HttpClientFactory);
               GenerateStrategyPricesService generateStrategyPricesService = new GenerateStrategyPricesService(HttpClientFactoryService.HttpClientFactory);
               await fetchAutoTraderService.FetchAutoTraderAdverts(logger, fetchGroups);
            }
            catch (Exception ex) { await EmailerService.LogException(ex, logger, "FetchAdverts"); }
            LoggingService.LogTiming(sw, "FetchAdverts", logger);


            //----------------------------------------------------------------
            // InStockNotOnPortal: Value and save those items in stock that are not found in AT.  Uses cache table.  OK
            // ----------------------------------------------------------------
            try
            {
               List<DealerGroupName> inStockNotInATGroups = ConfigService.BuildEnumList(ConfigService.InStockNotOnPortalGroups);
               VehicleValuationService valuationService = new VehicleValuationService(HttpClientFactoryService.HttpClientFactory);
               await valuationService.ValueAndSaveVehiclesInStockNotInAT(logger, inStockNotInATGroups);

            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "InStockNotOnPortal");
            }
            LoggingService.LogTiming(sw, "InStockNotOnPortal", logger);


            //----------------------------------------------------------------
            // OwnerAndSpecificColour: Get previous owners and manufacturer specific colour for the missing ones from AT.
            // ----------------------------------------------------------------
            try
            {
               UpdateOwnersAndSpecificColourService updateOwnersAndSpecificColourService = new UpdateOwnersAndSpecificColourService();
               await updateOwnersAndSpecificColourService.UpdateOwnersAndSpecificColour(logger, fetchGroups);
            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "OwnerAndSpecificColour");
            }
            LoggingService.LogTiming(sw, "OwnerAndSpecificColour", logger);



            

            // ----------------------------------------------------------------
            // CountPriceChanges: Quantify and update total price changes and value for each advert.  OK  
            // ----------------------------------------------------------------
            try
            {
               var measure = new MeasureDailyPriceMovesService();
               await measure.MeasureDailyPriceMoves(logger, fetchGroups);
            }
            catch (Exception ex) { await EmailerService.LogException(ex, logger, "CountPriceChanges"); }
            LoggingService.LogTiming(sw, "CountPriceChanges", logger);


            

            // ----------------------------------------------------------------
            // LeavingItems: now update the leaving deals.   not applicable
            // ----------------------------------------------------------------
            try
            {
               var updateLeavingItemsService = new UpdateLeavingItemsService();
               await updateLeavingItemsService.PopulateLeavingItems(logger, fetchGroups);  //ok
            }
            catch (Exception ex) { await EmailerService.LogException(ex, logger, "LeavingItems"); }
            LoggingService.LogTiming(sw, "LeavingItems", logger);

            // ----------------------------------------------------------------
            // StrategyPrices: update the strategy price for each snapshot.  ok, done
            // ----------------------------------------------------------------
            try
            {
               GenerateStrategyPricesService generateStrategyPricesService = new GenerateStrategyPricesService(HttpClientFactoryService.HttpClientFactory);
               await generateStrategyPricesService.GenerateStrategyPricesAndSave(logger, fetchGroups);
            }
            catch (Exception ex) { await EmailerService.LogException(ex, logger, "StrategyPrices"); }
            LoggingService.LogTiming(sw, "StrategyPrices", logger);

            


            // ----------------------------------------------------------------
            // UpdateDaysToSell: now update the days to sell for each snapshot (we are updating it based on the current selling price).  OK, updated
            // ----------------------------------------------------------------
            try
            {
               var updateDaysToSellService = new UpdateDaysToSellService(httpClient);
               await updateDaysToSellService.UpdateDaysToSell(logger, fetchGroups);
            }
            catch (Exception ex) { await EmailerService.LogException(ex, logger, "UpdateDaysToSell"); }
            LoggingService.LogTiming(sw, "UpdateDaysToSell", logger);

            // ----------------------------------------------------------------
            // CreateOptOuts: apply any opt-outs.  not applicable
            // ----------------------------------------------------------------
            try
            {
               List<DealerGroupName> optOutGroups = ConfigService.BuildEnumList(ConfigService.CreateOptOutsGroups);
               var createOverageOptOutsService = new CreateOverageOptOutsService();
               await createOverageOptOutsService.CreateOptOuts(logger, optOutGroups);  //ok
            }
            catch (Exception ex) { await EmailerService.LogException(ex, logger, "CreateOptOuts"); }
            LoggingService.LogTiming(sw, "CreateOptOuts", logger);


            // ----------------------------------------------------------------
            // CompetitorInformation: SPK-4244 now also get competitor information.  OK, updated
            // ----------------------------------------------------------------
            try
            {
               var getCompetitorSituationService = new GetCompetitorSituationService(httpClient);
               await getCompetitorSituationService.GetCompetitorInformationAndSave(logger, fetchGroups);
            }
            catch (Exception ex) { await EmailerService.LogException(ex, logger, "CompetitorInformation"); }
            LoggingService.LogTiming(sw, "CompetitorInformation", logger);




            // ----------------------------------------------------------------
            // GlobalParams: Update globalParams with last download date
            // ----------------------------------------------------------------
            try
            {
               var updateGLobalParamsService = new UpdateGlobalParamsLastUpdateDateService();
               await updateGLobalParamsService.UpdateDates(logger, fetchGroups);
            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "GlobalParams");
            }
            LoggingService.LogTiming(sw, "GlobalParams", logger);

            // ----------------------------------------------------------------
            // TriggerCache: trigger cache rebuilds
            // ----------------------------------------------------------------
            try
            {
               var triggerCacheRebuildService = new TriggerCacheRebuildService();
               await triggerCacheRebuildService.TriggerCacheRebuilds(logger, fetchGroups);
            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "TriggerCache");
            }
            LoggingService.LogTiming(sw, "TriggerCache", logger);





            // ----------------------------------------------------------------
            // GenerateAutoChangesWeekday: Weekday create changes and send emails, OK updated
            // ----------------------------------------------------------------
            if (ConstantMethodsService.IsWeekDay(DateTime.Today))
            {

               try
               {
                  //use our rules to generate prices changes
                  List<DealerGroupName> generateAutoChangesWeekdayGroups = ConfigService.BuildEnumList(ConfigService.FetchJob_GenerateChangesGroupsWeekday);
                  var generatePriceChangesService = new GeneratePriceChangesService(httpClient);
                  await generatePriceChangesService.GeneratePriceChanges(logger, generateAutoChangesWeekdayGroups);
               }
               catch (Exception ex)
               {
                  await EmailerService.LogException(ex, logger, "GenerateAutoChangesWeekday");
               }
               LoggingService.LogTiming(sw, "GenerateAutoChangesWeekday", logger);

               try
               //email a summary of the price changes
               {
                  List<DealerGroupName> sendEmailsWeekdayGroups = ConfigService.BuildEnumList(ConfigService.FetchJob_EmailChangesGroupsWeekday);
                  var reportTodayPriceChangesService = new ReportTodayPriceChangesGeneratedService();
                  await reportTodayPriceChangesService.SendEmails(logger, sendEmailsWeekdayGroups, false);
               }
               catch (Exception ex)
               {
                  await EmailerService.LogException(ex, logger, "SendEmailsWeekday");
               }
               LoggingService.LogTiming(sw, "SendEmailsWeekday", logger);
            }

            // ----------------------------------------------------------------
            // GenerateAutoChangesWeekend: Weekend create changes and send emails. already updated above
            // ----------------------------------------------------------------
            if (ConstantMethodsService.IsWeekend(DateTime.Today))
            {
               try
               //use our rules to generate prices changes
               {
                  List<DealerGroupName> generateAutoChangesWeekendGroups = ConfigService.BuildEnumList(ConfigService.FetchJob_EmailChangesGroupsWeekend);
                  var generatePriceChangesService = new GeneratePriceChangesService(httpClient);
                  await generatePriceChangesService.GeneratePriceChanges(logger, generateAutoChangesWeekendGroups); //only make weekend changes for those we are going to email
               }
               catch (Exception ex)
               {
                  await EmailerService.LogException(ex, logger, "GenerateAutoChangesWeekend");
               }
               LoggingService.LogTiming(sw, "GenerateAutoChangesWeekend", logger);

               try
               //email a summary of the price changes
               {
                  List<DealerGroupName> sendEmailsWeekendGroups = ConfigService.BuildEnumList(ConfigService.FetchJob_EmailChangesGroupsWeekend);
                  var reportTodayPriceChangesService = new ReportTodayPriceChangesGeneratedService();
                  await reportTodayPriceChangesService.SendEmails(logger, sendEmailsWeekendGroups, true);
               }
               catch (Exception ex)
               {
                  await EmailerService.LogException(ex, logger, "SendEmailWeekend");
               }
               LoggingService.LogTiming(sw, "SendEmailWeekend", logger);
            }


            // ----------------------------------------------------------------
            // DeleteOldValuations: Tidy away old valuations N/A
            // ----------------------------------------------------------------
            try
            //delete old valuations
            {
               List<DealerGroupName> deleteOldValuationsGroups = ConfigService.BuildEnumList(ConfigService.ValuationJob_Groups);
               var deleteOldValuationsService = new DeleteOldValuationsService();
               await deleteOldValuationsService.DeleteOldValuations(logger, deleteOldValuationsGroups);
            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "DeleteOldValuations");
            }
            LoggingService.LogTiming(sw, "DeleteOldValuations", logger);


            // ----------------------------------------------------------------
            // LocationOptimiser: Location Optimiser bits N/A
            // ----------------------------------------------------------------
            try
            {
               List<DealerGroupName> locationOptimiserGroups = ConfigService.BuildEnumList(ConfigService.FetchJob_LocationOptimiserGroups);
               GenerateStrategyPricesService generateStrategyPricesService = new GenerateStrategyPricesService(HttpClientFactoryService.HttpClientFactory);
               FetchAutoTraderService fetchAutoTraderService = new FetchAutoTraderService(HttpClientFactoryService.HttpClientFactory);
               await fetchAutoTraderService.MeasureOtherSiteRetailRatings(logger, locationOptimiserGroups);
               await generateStrategyPricesService.CalcStrategyPriceOtherSiteLocations(logger, locationOptimiserGroups);
            }
            catch (Exception ex)
            {
               await EmailerService.LogException(ex, logger, "LocationOptimiser");
            }
            LoggingService.LogTiming(sw, "LocationOptimiser", logger);








         }
         catch (Exception ex)
         {
            logger.Error($"General error within FetchAndGenerateJob: {ex.Message}");
            await EmailerService.LogException(ex, logger, "FetchAndGenerateAutoTraderPrices_Job");
         }


         logger.Info("Completed FetchAndGenerateAutoTraderPrices_Job");
         logger.Info("============================================================================================");
      }









   }

}

